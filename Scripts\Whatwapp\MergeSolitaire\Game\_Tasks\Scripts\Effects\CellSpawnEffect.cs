using UnityEngine;
using DG.Tweening;
using _Tasks.NewFeature.Effects;
using _Tasks.NewFeature.Settings;

namespace _Tasks.NewFeature.Effects
{
    /// <summary>
    /// Example implementation of cell spawn effects
    /// Demonstrates how to create modular effects with A/B testing support
    /// </summary>
    public class CellSpawnEffect : BaseGameEffect, ICellSpawnEffect
    {
        [Header("Cell Spawn Effect")]
        [SerializeField] private ParticleSystem _spawnParticles;
        [SerializeField] private SpriteRenderer _glowRenderer;
        [SerializeField] private Transform _effectTransform;

        private CellSpawnEffectConfig _spawnConfig;

        protected override void OnInitialize()
        {
            _spawnConfig = _config as CellSpawnEffectConfig;
            if (_spawnConfig == null)
            {
                Debug.LogError($"[{_effectId}] Invalid config type for CellSpawnEffect");
                return;
            }

            SetupVisualComponents();
        }

        protected override void OnExecute(EffectParameters parameters)
        {
            PlaySpawnAnimation(parameters.position, Vector2Int.zero, parameters.delay);
        }

        public void PlaySpawnAnimation(Vector3 position, Vector2Int coordinates, float delay = 0f)
        {
            if (_spawnConfig == null || !_spawnConfig.IsEnabled)
            {
                CompleteEffect();
                return;
            }

            // Position the effect
            transform.position = position;
            
            // Create animation sequence
            var sequence = CreateSequence();
            
            // Add delay if specified
            if (delay > 0f)
            {
                sequence.AppendInterval(delay);
            }

            // Setup initial state
            SetupInitialState();

            // Create scale animation
            if (_effectTransform != null)
            {
                var scaleTween = _effectTransform.DOScale(Vector3.one, ApplyIntensity(_spawnConfig.duration))
                    .SetEase(Ease.OutBack)
                    .From(Vector3.zero);
                
                // Apply custom scale curve if available
                if (_spawnConfig.scaleCurve != null && _spawnConfig.scaleCurve.keys.Length > 0)
                {
                    scaleTween.SetEase(_spawnConfig.scaleCurve);
                }
                
                sequence.Join(scaleTween);
            }

            // Create alpha animation
            if (_glowRenderer != null && _spawnConfig.enableGlow)
            {
                var glowColor = ApplyIntensity(_spawnConfig.spawnColor);
                glowColor.a = 0f;
                _glowRenderer.color = glowColor;
                
                var alphaTween = _glowRenderer.DOFade(ApplyIntensity(_spawnConfig.glowIntensity), 
                    ApplyIntensity(_spawnConfig.duration) * 0.5f)
                    .SetEase(Ease.OutQuad);
                
                // Apply custom alpha curve if available
                if (_spawnConfig.alphaCurve != null && _spawnConfig.alphaCurve.keys.Length > 0)
                {
                    alphaTween.SetEase(_spawnConfig.alphaCurve);
                }
                
                sequence.Join(alphaTween);
                
                // Fade out glow
                sequence.Append(_glowRenderer.DOFade(0f, ApplyIntensity(_spawnConfig.duration) * 0.3f));
            }

            // Trigger particles
            if (_spawnParticles != null && _spawnConfig.enableParticles)
            {
                sequence.AppendCallback(() => TriggerParticles());
            }

            // Play the sequence
            sequence.Play();

            if (_debugMode)
                Debug.Log($"[{_effectId}] Playing spawn animation at {position} with delay {delay}");
        }

        private void SetupVisualComponents()
        {
            // Setup particle system
            if (_spawnParticles != null && _spawnConfig != null)
            {
                var main = _spawnParticles.main;
                main.startColor = ApplyIntensity(_spawnConfig.spawnColor);
                main.maxParticles = Mathf.RoundToInt(20 * ApplyIntensity(1f));
                
                var emission = _spawnParticles.emission;
                emission.enabled = _spawnConfig.enableParticles;
            }

            // Setup glow renderer
            if (_glowRenderer != null && _spawnConfig != null)
            {
                _glowRenderer.color = _spawnConfig.spawnColor;
                _glowRenderer.gameObject.SetActive(_spawnConfig.enableGlow);
            }

            // Find effect transform if not assigned
            if (_effectTransform == null)
            {
                _effectTransform = transform;
            }
        }

        private void SetupInitialState()
        {
            // Reset transform
            if (_effectTransform != null)
            {
                _effectTransform.localScale = Vector3.zero;
            }

            // Reset glow
            if (_glowRenderer != null && _spawnConfig.enableGlow)
            {
                var color = _spawnConfig.spawnColor;
                color.a = 0f;
                _glowRenderer.color = color;
            }
        }

        private void TriggerParticles()
        {
            if (_spawnParticles != null && _spawnConfig.enableParticles)
            {
                _spawnParticles.Play();
                
                if (_debugMode)
                    Debug.Log($"[{_effectId}] Triggered spawn particles");
            }
        }

        protected override void OnStop()
        {
            // Stop particles
            if (_spawnParticles != null)
            {
                _spawnParticles.Stop();
            }

            // Reset visual state
            if (_effectTransform != null)
            {
                _effectTransform.localScale = Vector3.one;
            }

            if (_glowRenderer != null)
            {
                var color = _glowRenderer.color;
                color.a = 0f;
                _glowRenderer.color = color;
            }
        }

        #region Editor Support

#if UNITY_EDITOR
        [ContextMenu("Test Spawn Effect")]
        private void TestSpawnEffect()
        {
            if (Application.isPlaying)
            {
                PlaySpawnAnimation(transform.position, Vector2Int.zero, 0f);
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Draw effect radius in editor
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, 1f);
            
            if (_spawnConfig != null)
            {
                Gizmos.color = _spawnConfig.spawnColor;
                Gizmos.DrawWireSphere(transform.position, 0.5f);
            }
        }
#endif

        #endregion

        #region Variant-Specific Behavior

        /// <summary>
        /// Apply variant-specific modifications to the effect
        /// This method can be overridden to create dramatically different behaviors per variant
        /// </summary>
        protected virtual void ApplyVariantModifications()
        {
            if (_spawnConfig == null) return;

            switch (_spawnConfig.VariantName.ToLower())
            {
                case "enhanced":
                    ApplyEnhancedVariant();
                    break;
                case "minimal":
                    ApplyMinimalVariant();
                    break;
                case "default":
                default:
                    // Default behavior already applied
                    break;
            }
        }

        private void ApplyEnhancedVariant()
        {
            // Enhanced variant could have additional visual elements
            if (_spawnParticles != null)
            {
                var main = _spawnParticles.main;
                main.maxParticles *= 2; // More particles
                
                var shape = _spawnParticles.shape;
                shape.radius *= 1.5f; // Larger spawn area
            }
        }

        private void ApplyMinimalVariant()
        {
            // Minimal variant reduces visual complexity
            if (_spawnParticles != null)
            {
                var emission = _spawnParticles.emission;
                emission.enabled = false; // No particles
            }

            if (_glowRenderer != null)
            {
                _glowRenderer.gameObject.SetActive(false); // No glow
            }
        }

        #endregion
    }
}
