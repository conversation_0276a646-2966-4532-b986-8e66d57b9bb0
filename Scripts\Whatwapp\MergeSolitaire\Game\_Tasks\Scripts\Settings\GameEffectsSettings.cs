using UnityEngine;
using _Tasks.NewFeature.Effects;

namespace _Tasks.NewFeature.Settings
{
    /// <summary>
    /// Main configuration for all game effects
    /// Supports A/B testing through variant selection
    /// </summary>
    [CreateAssetMenu(menuName = "MergeSolitaire/Settings/Game Effects", fileName = "GameEffectsSettings")]
    public class GameEffectsSettings : ScriptableObject
    {
        [Header("A/B Testing")]
        [SerializeField] private string _currentVariant = "default";
        [SerializeField] private bool _enableABTesting = false;
        
        [Header("Effect Configurations")]
        [SerializeField] private CellSpawnEffectConfig[] _cellSpawnVariants;
        [SerializeField] private BlockMovementEffectConfig[] _movementVariants;
        [SerializeField] private BlockArrivalEffectConfig[] _arrivalVariants;
        [SerializeField] private MergeEffectConfig[] _mergeVariants;
        [SerializeField] private ScoreEffectConfig[] _scoreVariants;
        
        [Header("Performance")]
        [SerializeField] private bool _enablePerformanceMonitoring = true;
        [SerializeField] private int _maxConcurrentEffects = 50;
        [SerializeField] private float _effectCullingDistance = 20f;

        public string CurrentVariant => _currentVariant;
        public bool EnableABTesting => _enableABTesting;
        public bool EnablePerformanceMonitoring => _enablePerformanceMonitoring;
        public int MaxConcurrentEffects => _maxConcurrentEffects;
        public float EffectCullingDistance => _effectCullingDistance;

        public CellSpawnEffectConfig GetCellSpawnConfig(string variant = null)
        {
            return GetConfigByVariant(_cellSpawnVariants, variant ?? _currentVariant);
        }

        public BlockMovementEffectConfig GetMovementConfig(string variant = null)
        {
            return GetConfigByVariant(_movementVariants, variant ?? _currentVariant);
        }

        public BlockArrivalEffectConfig GetArrivalConfig(string variant = null)
        {
            return GetConfigByVariant(_arrivalVariants, variant ?? _currentVariant);
        }

        public MergeEffectConfig GetMergeConfig(string variant = null)
        {
            return GetConfigByVariant(_mergeVariants, variant ?? _currentVariant);
        }

        public ScoreEffectConfig GetScoreConfig(string variant = null)
        {
            return GetConfigByVariant(_scoreVariants, variant ?? _currentVariant);
        }

        private T GetConfigByVariant<T>(T[] configs, string variant) where T : EffectConfigBase
        {
            if (configs == null || configs.Length == 0) return null;

            foreach (var config in configs)
            {
                if (config.VariantName == variant)
                    return config;
            }

            // Fallback to first config if variant not found
            return configs[0];
        }

        public void SetVariant(string variant)
        {
            _currentVariant = variant;
        }
    }

    /// <summary>
    /// Base class for all effect configurations
    /// </summary>
    [System.Serializable]
    public abstract class EffectConfigBase : IEffectConfig
    {
        [SerializeField] protected string _variantName = "default";
        [SerializeField] protected bool _isEnabled = true;
        [SerializeField] protected float _intensityMultiplier = 1f;

        public string VariantName => _variantName;
        public bool IsEnabled => _isEnabled;
        public float IntensityMultiplier => _intensityMultiplier;
    }

    /// <summary>
    /// Configuration for cell spawn effects
    /// </summary>
    [System.Serializable]
    public class CellSpawnEffectConfig : EffectConfigBase
    {
        [Header("Spawn Animation")]
        public AnimationCurve scaleCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        public AnimationCurve alphaCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        public float duration = 0.3f;
        public float maxDelay = 0.1f;
        
        [Header("Visual Effects")]
        public bool enableParticles = true;
        public bool enableGlow = false;
        public Color spawnColor = Color.white;
        public float glowIntensity = 1f;
    }

    /// <summary>
    /// Configuration for block movement effects
    /// </summary>
    [System.Serializable]
    public class BlockMovementEffectConfig : EffectConfigBase
    {
        [Header("Movement Animation")]
        public AnimationCurve movementCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        public bool enableTrail = true;
        public bool enableAnticipation = false;
        
        [Header("Trail Settings")]
        public Color trailColor = Color.white;
        public float trailDuration = 0.2f;
        public int trailSegments = 10;
        
        [Header("Anticipation")]
        public float anticipationScale = 1.1f;
        public float anticipationDuration = 0.1f;
    }

    /// <summary>
    /// Configuration for block arrival effects
    /// </summary>
    [System.Serializable]
    public class BlockArrivalEffectConfig : EffectConfigBase
    {
        [Header("Impact Effects")]
        public bool enableScreenShake = true;
        public bool enableParticles = true;
        public bool enableRipple = false;
        
        [Header("Screen Shake")]
        public float shakeIntensity = 0.1f;
        public float shakeDuration = 0.15f;
        
        [Header("Particles")]
        public int particleCount = 20;
        public float particleSpeed = 5f;
        public Color particleColor = Color.white;
        
        [Header("Ripple")]
        public float rippleRadius = 2f;
        public float rippleDuration = 0.5f;
    }

    /// <summary>
    /// Configuration for merge effects
    /// </summary>
    [System.Serializable]
    public class MergeEffectConfig : EffectConfigBase
    {
        [Header("Merge Animation")]
        public float convergeDuration = 0.3f;
        public float explosionDuration = 0.2f;
        public AnimationCurve convergeEase = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Visual Effects")]
        public bool enableEnergyBeams = true;
        public bool enableExplosion = true;
        public bool enableScreenFlash = false;
        
        [Header("Energy Beams")]
        public Color beamColor = Color.cyan;
        public float beamWidth = 0.1f;
        public float beamDuration = 0.25f;
        
        [Header("Explosion")]
        public int explosionParticles = 50;
        public float explosionRadius = 1.5f;
        public Color explosionColor = Color.yellow;
    }

    /// <summary>
    /// Configuration for score effects
    /// </summary>
    [System.Serializable]
    public class ScoreEffectConfig : EffectConfigBase
    {
        [Header("Score Counter")]
        public float counterAnimationSpeed = 10f;
        public bool enableColorChange = true;
        public bool enableScaleEffect = true;
        
        [Header("Floating Text")]
        public float floatingDuration = 1.5f;
        public Vector3 floatingOffset = new Vector3(0, 2, 0);
        public AnimationCurve floatingCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
            
        [Header("Milestones")]
        public int[] milestoneThresholds = { 100, 500, 1000, 5000, 10000 };
        public bool enableMilestoneParticles = true;
        public bool enableMilestoneSound = true;
        public Color milestoneColor = Color.yellow;
        
        [Header("Colors")]
        public Color positiveScoreColor = Color.green;
        public Color milestoneTextColor = Color.yellow;
        public Color normalTextColor = Color.white;
    }
}
