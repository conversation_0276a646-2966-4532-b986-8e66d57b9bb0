using UnityEngine;
using System.Collections.Generic;
using _Tasks.NewFeature.Managers;
using _Tasks.NewFeature.Settings;

namespace _Tasks.NewFeature.ABTesting
{
    /// <summary>
    /// Manages A/B testing for game effects
    /// Provides integration points for external A/B testing frameworks
    /// Handles variant assignment and switching
    /// </summary>
    public class EffectVariantManager : MonoBehaviour
    {
        [Header("A/B Testing Configuration")]
        [SerializeField] private GameEffectsSettings _effectsSettings;
        [SerializeField] private bool _enableABTesting = true;
        [SerializeField] private string _defaultVariant = "default";
        
        [Header("Variant Definitions")]
        [SerializeField] private VariantDefinition[] _availableVariants;
        
        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;
        [SerializeField] private bool _forceVariant = false;
        [SerializeField] private string _forcedVariantName = "default";

        // Player variant tracking
        private Dictionary<string, string> _playerVariants = new Dictionary<string, string>();
        private string _currentPlayerVariant = "default";

        // Singleton for easy access
        public static EffectVariantManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeVariantManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        #region Initialization

        private void InitializeVariantManager()
        {
            if (_effectsSettings == null)
            {
                Debug.LogError("[EffectVariantManager] Effects settings not assigned!");
                return;
            }

            // Set up default variant
            _currentPlayerVariant = _defaultVariant;

            // Apply forced variant if enabled (for testing)
            if (_forceVariant && !string.IsNullOrEmpty(_forcedVariantName))
            {
                _currentPlayerVariant = _forcedVariantName;
                Debug.Log($"[EffectVariantManager] Forcing variant: {_forcedVariantName}");
            }

            // Apply the initial variant
            ApplyVariant(_currentPlayerVariant);

            if (_debugMode)
                Debug.Log($"[EffectVariantManager] Initialized with variant: {_currentPlayerVariant}");
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get the variant for a specific player
        /// Integrates with external A/B testing frameworks
        /// </summary>
        /// <param name="playerId">Player identifier</param>
        /// <returns>Variant name for the player</returns>
        public string GetPlayerVariant(string playerId)
        {
            if (!_enableABTesting)
                return _defaultVariant;

            // Check if we already have a variant for this player
            if (_playerVariants.ContainsKey(playerId))
            {
                return _playerVariants[playerId];
            }

            // Determine variant for new player
            string variant = DetermineVariantForPlayer(playerId);
            _playerVariants[playerId] = variant;

            if (_debugMode)
                Debug.Log($"[EffectVariantManager] Assigned variant '{variant}' to player {playerId}");

            return variant;
        }

        /// <summary>
        /// Set the variant for the current session
        /// </summary>
        /// <param name="variant">Variant name to apply</param>
        public void SetCurrentVariant(string variant)
        {
            if (_currentPlayerVariant == variant) return;

            _currentPlayerVariant = variant;
            ApplyVariant(variant);

            if (_debugMode)
                Debug.Log($"[EffectVariantManager] Switched to variant: {variant}");
        }

        /// <summary>
        /// Get the current active variant
        /// </summary>
        /// <returns>Current variant name</returns>
        public string GetCurrentVariant()
        {
            return _currentPlayerVariant;
        }

        /// <summary>
        /// Get all available variants
        /// </summary>
        /// <returns>Array of available variant names</returns>
        public string[] GetAvailableVariants()
        {
            if (_availableVariants == null) return new string[] { _defaultVariant };

            var variants = new string[_availableVariants.Length];
            for (int i = 0; i < _availableVariants.Length; i++)
            {
                variants[i] = _availableVariants[i].variantName;
            }
            return variants;
        }

        /// <summary>
        /// Check if a variant exists
        /// </summary>
        /// <param name="variantName">Variant name to check</param>
        /// <returns>True if variant exists</returns>
        public bool VariantExists(string variantName)
        {
            if (_availableVariants == null) return variantName == _defaultVariant;

            foreach (var variant in _availableVariants)
            {
                if (variant.variantName == variantName)
                    return true;
            }
            return false;
        }

        #endregion

        #region Variant Assignment Logic

        /// <summary>
        /// Determine which variant a player should get
        /// Override this method to integrate with external A/B testing frameworks
        /// </summary>
        /// <param name="playerId">Player identifier</param>
        /// <returns>Variant name</returns>
        protected virtual string DetermineVariantForPlayer(string playerId)
        {
            // Default implementation: simple hash-based assignment
            if (_availableVariants == null || _availableVariants.Length == 0)
                return _defaultVariant;

            // Use player ID hash for consistent assignment
            int hash = playerId.GetHashCode();
            if (hash < 0) hash = -hash;
            
            int variantIndex = hash % _availableVariants.Length;
            return _availableVariants[variantIndex].variantName;
        }

        /// <summary>
        /// Apply a variant to the effects system
        /// </summary>
        /// <param name="variant">Variant name to apply</param>
        private void ApplyVariant(string variant)
        {
            if (_effectsSettings != null)
            {
                _effectsSettings.SetVariant(variant);
            }

            var effectsManager = GameEffectsManager.Instance;
            if (effectsManager != null)
            {
                effectsManager.SwitchToVariant(variant);
            }
        }

        #endregion

        #region External Framework Integration

        /// <summary>
        /// Integration point for Firebase Remote Config
        /// Call this method when Remote Config values are fetched
        /// </summary>
        /// <param name="configKey">Remote config key for variant</param>
        /// <param name="defaultValue">Default value if config not available</param>
        public void ApplyRemoteConfigVariant(string configKey, string defaultValue = null)
        {
            // Example integration with Firebase Remote Config
            // string variant = FirebaseRemoteConfig.DefaultInstance.GetValue(configKey).StringValue;
            
            string variant = defaultValue ?? _defaultVariant;
            
            if (!string.IsNullOrEmpty(variant) && VariantExists(variant))
            {
                SetCurrentVariant(variant);
            }

            if (_debugMode)
                Debug.Log($"[EffectVariantManager] Applied remote config variant: {variant}");
        }

        /// <summary>
        /// Integration point for custom A/B testing frameworks
        /// </summary>
        /// <param name="experimentId">Experiment identifier</param>
        /// <param name="playerId">Player identifier</param>
        /// <returns>Assigned variant</returns>
        public string GetExperimentVariant(string experimentId, string playerId)
        {
            // Integration point for external A/B testing services
            // Example: return ABTestingService.GetVariant(experimentId, playerId);
            
            // Default implementation
            return GetPlayerVariant(playerId);
        }

        /// <summary>
        /// Track variant assignment for analytics
        /// </summary>
        /// <param name="playerId">Player identifier</param>
        /// <param name="variant">Assigned variant</param>
        /// <param name="experimentId">Optional experiment identifier</param>
        public void TrackVariantAssignment(string playerId, string variant, string experimentId = null)
        {
            // Integration point for analytics tracking
            // Example: Analytics.CustomEvent("effect_variant_assigned", new Dictionary<string, object>
            // {
            //     {"player_id", playerId},
            //     {"variant", variant},
            //     {"experiment_id", experimentId}
            // });

            if (_debugMode)
                Debug.Log($"[EffectVariantManager] Tracked variant assignment: {playerId} -> {variant}");
        }

        #endregion

        #region Debug and Testing

        /// <summary>
        /// Force a specific variant for testing purposes
        /// </summary>
        /// <param name="variant">Variant to force</param>
        public void ForceVariantForTesting(string variant)
        {
            if (!VariantExists(variant))
            {
                Debug.LogWarning($"[EffectVariantManager] Variant '{variant}' does not exist!");
                return;
            }

            _forceVariant = true;
            _forcedVariantName = variant;
            SetCurrentVariant(variant);

            Debug.Log($"[EffectVariantManager] Forced variant for testing: {variant}");
        }

        /// <summary>
        /// Clear forced variant and return to normal assignment
        /// </summary>
        public void ClearForcedVariant()
        {
            _forceVariant = false;
            _forcedVariantName = "";
            
            // Reassign variant based on normal logic
            string normalVariant = DetermineVariantForPlayer(SystemInfo.deviceUniqueIdentifier);
            SetCurrentVariant(normalVariant);

            Debug.Log($"[EffectVariantManager] Cleared forced variant, using: {normalVariant}");
        }

        /// <summary>
        /// Get variant statistics for debugging
        /// </summary>
        /// <returns>Dictionary of variant assignments</returns>
        public Dictionary<string, int> GetVariantStatistics()
        {
            var stats = new Dictionary<string, int>();
            
            foreach (var assignment in _playerVariants.Values)
            {
                if (stats.ContainsKey(assignment))
                    stats[assignment]++;
                else
                    stats[assignment] = 1;
            }

            return stats;
        }

        #endregion
    }

    /// <summary>
    /// Definition of an A/B testing variant
    /// </summary>
    [System.Serializable]
    public class VariantDefinition
    {
        public string variantName;
        public string description;
        [Range(0f, 1f)]
        public float trafficAllocation = 0.5f; // Percentage of users to assign this variant
        public bool isEnabled = true;
    }
}
