using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using _Tasks.Events;
using Whatwapp.Core.Audio;
using Whatwapp.MergeSolitaire.Game;

namespace _Tasks.NewFeature.VFX
{
    /// <summary>
    /// Centralized VFX Manager for handling explosion effects, screen shake, and particle systems
    /// Provides clear and impactful visual feedback for bomb explosions and block destruction
    /// </summary>
    public class VFXManager : MonoBehaviour
    {
        [Header("Explosion Effects")]
        [SerializeField] private GameObject _explosionParticlePrefab;
        [SerializeField] private GameObject _subExplosionParticlePrefab;
        [SerializeField] private float _mainExplosionDuration = 0.5f;
        [SerializeField] private float _subExplosionDelay = 0.1f;
        [SerializeField] private float _subExplosionSpread = 0.05f;
        
        [Header("Screen Shake")]
        [SerializeField] private float _explosionShakeIntensity = 0.3f;
        [SerializeField] private float _explosionShakeDuration = 0.4f;
        [SerializeField] private int _explosionShakeVibrato = 10;
        [SerializeField] private float _explosionShakeRandomness = 90f;
        
        [Header("Block Destruction Effects")]
        [SerializeField] private Color _destructionFlashColor = Color.white;
        [SerializeField] private float _destructionFlashDuration = 0.15f;
        [SerializeField] private float _destructionScaleMultiplier = 1.3f;
        [SerializeField] private float _destructionFadeDuration = 0.2f;
        
        [Header("Audio")]
        [SerializeField] private string _explosionSfxName = Consts.SFX_BombExplosion;
        [SerializeField] private string _subExplosionSfxName = Consts.SFX_BlockDestroy;
        [SerializeField] private float _explosionVolume = 0.8f;
        [SerializeField] private float _subExplosionVolume = 0.6f;
        
        [Header("References")]
        [SerializeField] private Camera _mainCamera;
        [SerializeField] private AnimationSettings _animationSettings;
        
        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        private static VFXManager _instance;
        public static VFXManager Instance => _instance;

        private SFXManager _sfxManager;
        private List<Coroutine> _activeEffects = new List<Coroutine>();

        #region Unity Lifecycle

        private void Awake()
        {
            // Singleton pattern
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Auto-find references if not assigned
            if (_mainCamera == null)
                _mainCamera = Camera.main;

            _sfxManager = SFXManager.Instance;

            // Subscribe to explosion events
            EventManager.Subscribe<BombExplosionEvent>(OnBombExplosion);
            EventManager.Subscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);
            EventManager.Subscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<BombExplosionEvent>(OnBombExplosion);
            EventManager.Unsubscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);
            EventManager.Unsubscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);

            // Stop all active effects
            StopAllEffects();
        }

        private void OnValidate()
        {
            // Validate references in editor
            if (_mainCamera == null)
            {
                _mainCamera = Camera.main;
                if (_mainCamera == null && Application.isPlaying)
                {
                    Debug.LogWarning("[VFXManager] Main camera reference is missing!");
                }
            }

            // Validate singleton instance
            if (Application.isPlaying && _instance != null && _instance != this)
            {
                Debug.LogWarning("[VFXManager] Multiple VFXManager instances detected! Only one should exist.");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle bomb explosion events with main explosion effect and screen shake only
        /// </summary>
        private void OnBombExplosion(BombExplosionEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Processing bomb explosion at {eventData.ExplosionCenter}");

            // Play main explosion effect
            PlayMainExplosionEffect(eventData.ExplosionCenter, eventData.ExplosionRadius);

            // Trigger screen shake
            TriggerScreenShake(_explosionShakeIntensity, _explosionShakeDuration);

            // Play explosion sound
            PlayExplosionSound();
        }

        /// <summary>
        /// Handle bomb destruction marked events - trigger sub-explosions with proper timing
        /// </summary>
        private void OnBombDestructionMarked(BombDestructionMarkedEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Bomb destruction marked for {eventData.BlockPositions.Count} blocks");

            // Start sub-explosion coroutine with the captured block positions
            if (eventData.BlockPositions != null && eventData.BlockPositions.Count > 0)
            {
                StartCoroutine(PlaySubExplosionsCoroutine(eventData.BlockPositions));
            }
        }

        /// <summary>
        /// Handle block destruction requests with visual feedback
        /// </summary>
        private void OnBlockDestructionRequested(BlockDestructionRequestEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Processing block destruction request for {eventData.BlocksToDestroy.Count} blocks");

            // Apply destruction effects to each block before they get destroyed
            foreach (var block in eventData.BlocksToDestroy)
            {
                if (block != null && block.Visual != null)
                {
                    ApplyBlockDestructionEffect(block);
                }
            }
        }

        #endregion

        #region Explosion Effects

        /// <summary>
        /// Play the main explosion effect at the bomb location
        /// </summary>
        private void PlayMainExplosionEffect(Vector3 position, int radius)
        {
            if (_explosionParticlePrefab != null)
            {
                var explosion = Instantiate(_explosionParticlePrefab, position, Quaternion.identity);
                
                // Scale particle system based on explosion radius
                var particleSystem = explosion.GetComponent<ParticleSystem>();
                if (particleSystem != null)
                {
                    var main = particleSystem.main;
                    main.startSize = main.startSize.constant * (1f + radius * 0.2f);
                }
                
                // Auto-destroy after duration
                Destroy(explosion, _mainExplosionDuration);
                
                if (_debugMode)
                    Debug.Log($"[VFXManager] Main explosion effect spawned at {position}");
            }
        }

        /// <summary>
        /// Play sub-explosion effects at captured positions with staggered timing
        /// </summary>
        private IEnumerator PlaySubExplosionsCoroutine(List<Vector3> blockPositions)
        {
            for (int i = 0; i < blockPositions.Count; i++)
            {
                // Add slight random delay for more natural effect
                float delay = i * _subExplosionDelay + Random.Range(0f, _subExplosionSpread);
                yield return new WaitForSeconds(delay);

                PlaySubExplosionEffect(blockPositions[i]);
                PlaySubExplosionSound();
            }
        }

        /// <summary>
        /// Play individual sub-explosion effect at block position
        /// </summary>
        private void PlaySubExplosionEffect(Vector3 position)
        {
            if (_subExplosionParticlePrefab != null)
            {
                var subExplosion = Instantiate(_subExplosionParticlePrefab, position, Quaternion.identity);
                
                // Auto-destroy after a short duration
                Destroy(subExplosion, 1f);
                
                if (_debugMode)
                    Debug.Log($"[VFXManager] Sub-explosion effect spawned at {position}");
            }
        }

        #endregion

        #region Screen Shake

        /// <summary>
        /// Trigger camera screen shake effect
        /// </summary>
        public void TriggerScreenShake(float intensity = -1f, float duration = -1f)
        {
            if (_mainCamera == null) return;

            // Use default values if not specified
            if (intensity < 0) intensity = _explosionShakeIntensity;
            if (duration < 0) duration = _explosionShakeDuration;

            // Apply screen shake using DOTween
            _mainCamera.transform.DOShakePosition(
                duration,
                intensity,
                _explosionShakeVibrato,
                _explosionShakeRandomness,
                false,
                true
            );

            if (_debugMode)
                Debug.Log($"[VFXManager] Screen shake triggered - Intensity: {intensity}, Duration: {duration}");
        }

        /// <summary>
        /// Trigger a sub-explosion effect at a specific position
        /// </summary>
        public void TriggerSubExplosionAt(Vector3 position)
        {
            PlaySubExplosionEffect(position);
            PlaySubExplosionSound();

            if (_debugMode)
                Debug.Log($"[VFXManager] Sub-explosion triggered at {position}");
        }

        #endregion

        #region Block Destruction Effects

        /// <summary>
        /// Apply visual destruction effect to a specific block
        /// </summary>
        private void ApplyBlockDestructionEffect(Block block)
        {
            if (block?.Visual == null) return;

            var visual = block.Visual;
            var spriteRenderer = visual.GetComponent<SpriteRenderer>();
            
            if (spriteRenderer != null)
            {
                // Flash effect
                var originalColor = spriteRenderer.color;
                var flashSequence = DOTween.Sequence();
                
                flashSequence.Append(spriteRenderer.DOColor(_destructionFlashColor, _destructionFlashDuration * 0.5f))
                    .Append(spriteRenderer.DOColor(originalColor, _destructionFlashDuration * 0.5f));
                
                // Scale effect
                var originalScale = visual.transform.localScale;
                visual.transform.DOScale(originalScale * _destructionScaleMultiplier, _destructionFlashDuration)
                    .OnComplete(() => {
                        // Fade out effect
                        spriteRenderer.DOFade(0f, _destructionFadeDuration);
                        visual.transform.DOScale(originalScale * 0.8f, _destructionFadeDuration);
                    });
            }
        }

        #endregion

        #region Audio Effects

        /// <summary>
        /// Play main explosion sound effect
        /// </summary>
        private void PlayExplosionSound()
        {
            if (_sfxManager != null && !string.IsNullOrEmpty(_explosionSfxName))
            {
                _sfxManager.PlayOneShot(_explosionSfxName, _explosionVolume);
            }
        }

        /// <summary>
        /// Play sub-explosion sound effect
        /// </summary>
        private void PlaySubExplosionSound()
        {
            if (_sfxManager != null && !string.IsNullOrEmpty(_subExplosionSfxName))
            {
                _sfxManager.PlayOneShot(_subExplosionSfxName, _subExplosionVolume);
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Stop all active visual effects
        /// </summary>
        public void StopAllEffects()
        {
            foreach (var effect in _activeEffects)
            {
                if (effect != null)
                {
                    StopCoroutine(effect);
                }
            }
            _activeEffects.Clear();
        }

        /// <summary>
        /// Check if VFX system is ready
        /// </summary>
        public bool IsReady()
        {
            return _mainCamera != null && _sfxManager != null;
        }

        #endregion
    }
}
