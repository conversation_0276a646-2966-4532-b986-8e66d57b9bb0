using UnityEngine;
using System.Collections.Generic;
using _Tasks.Events;
using _Tasks.NewFeature.Effects;
using _Tasks.NewFeature.Settings;

namespace _Tasks.NewFeature.Managers
{
    /// <summary>
    /// Central manager for all game effects
    /// Coordinates visual feedback and integrates with existing EventManager system
    /// Supports A/B testing through configurable effect variants
    /// </summary>
    public class GameEffectsManager : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private GameEffectsSettings _effectsSettings;
        
        [Header("Effect Providers")]
        [SerializeField] private GameObject _cellSpawnEffectPrefab;
        [SerializeField] private GameObject _movementEffectPrefab;
        [SerializeField] private GameObject _arrivalEffectPrefab;
        [SerializeField] private GameObject _mergeEffectPrefab;
        [SerializeField] private GameObject _scoreEffectPrefab;

        // Effect instances
        private ICellSpawnEffect _cellSpawnEffect;
        private IBlockMovementEffect _movementEffect;
        private IBlockArrivalEffect _arrivalEffect;
        private IMergeEffect _mergeEffect;
        private IScoreEffect _scoreEffect;

        // Performance tracking
        private Dictionary<string, float> _effectPerformance = new Dictionary<string, float>();
        private int _activeEffectCount = 0;

        // Singleton pattern for easy access
        public static GameEffectsManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeEffects();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SubscribeToEvents();
        }

        private void OnDestroy()
        {
            UnsubscribeFromEvents();
            if (Instance == this)
            {
                Instance = null;
            }
        }

        #region Initialization

        private void InitializeEffects()
        {
            if (_effectsSettings == null)
            {
                Debug.LogError("[GameEffectsManager] Effects settings not assigned!");
                return;
            }

            // Initialize effect instances based on current variant
            InitializeCellSpawnEffect();
            InitializeMovementEffect();
            InitializeArrivalEffect();
            InitializeMergeEffect();
            InitializeScoreEffect();

            Debug.Log($"[GameEffectsManager] Initialized with variant: {_effectsSettings.CurrentVariant}");
        }

        private void InitializeCellSpawnEffect()
        {
            if (_cellSpawnEffectPrefab != null)
            {
                var effectObj = Instantiate(_cellSpawnEffectPrefab, transform);
                _cellSpawnEffect = effectObj.GetComponent<ICellSpawnEffect>();
                _cellSpawnEffect?.Initialize(_effectsSettings.GetCellSpawnConfig());
            }
        }

        private void InitializeMovementEffect()
        {
            if (_movementEffectPrefab != null)
            {
                var effectObj = Instantiate(_movementEffectPrefab, transform);
                _movementEffect = effectObj.GetComponent<IBlockMovementEffect>();
                _movementEffect?.Initialize(_effectsSettings.GetMovementConfig());
            }
        }

        private void InitializeArrivalEffect()
        {
            if (_arrivalEffectPrefab != null)
            {
                var effectObj = Instantiate(_arrivalEffectPrefab, transform);
                _arrivalEffect = effectObj.GetComponent<IBlockArrivalEffect>();
                _arrivalEffect?.Initialize(_effectsSettings.GetArrivalConfig());
            }
        }

        private void InitializeMergeEffect()
        {
            if (_mergeEffectPrefab != null)
            {
                var effectObj = Instantiate(_mergeEffectPrefab, transform);
                _mergeEffect = effectObj.GetComponent<IMergeEffect>();
                _mergeEffect?.Initialize(_effectsSettings.GetMergeConfig());
            }
        }

        private void InitializeScoreEffect()
        {
            if (_scoreEffectPrefab != null)
            {
                var effectObj = Instantiate(_scoreEffectPrefab, transform);
                _scoreEffect = effectObj.GetComponent<IScoreEffect>();
                _scoreEffect?.Initialize(_effectsSettings.GetScoreConfig());
            }
        }

        #endregion

        #region Event Subscription

        private void SubscribeToEvents()
        {
            EventManager.Subscribe<CellSpawnEvent>(OnCellSpawn);
            EventManager.Subscribe<BlockMovementEvent>(OnBlockMovement);
            EventManager.Subscribe<BlockArrivalEvent>(OnBlockArrival);
            EventManager.Subscribe<BlockMergeStartEvent>(OnMergeStart);
            EventManager.Subscribe<BlockMergeCompleteEvent>(OnMergeComplete);
            EventManager.Subscribe<ScoreUpdateEvent>(OnScoreUpdate);
            EventManager.Subscribe<FloatingScoreEvent>(OnFloatingScore);
            EventManager.Subscribe<ScoreMilestoneEvent>(OnScoreMilestone);
            EventManager.Subscribe<EffectVariantRequestEvent>(OnVariantRequest);
        }

        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<CellSpawnEvent>(OnCellSpawn);
            EventManager.Unsubscribe<BlockMovementEvent>(OnBlockMovement);
            EventManager.Unsubscribe<BlockArrivalEvent>(OnBlockArrival);
            EventManager.Unsubscribe<BlockMergeStartEvent>(OnMergeStart);
            EventManager.Unsubscribe<BlockMergeCompleteEvent>(OnMergeComplete);
            EventManager.Unsubscribe<ScoreUpdateEvent>(OnScoreUpdate);
            EventManager.Unsubscribe<FloatingScoreEvent>(OnFloatingScore);
            EventManager.Unsubscribe<ScoreMilestoneEvent>(OnScoreMilestone);
            EventManager.Unsubscribe<EffectVariantRequestEvent>(OnVariantRequest);
        }

        #endregion

        #region Event Handlers

        private void OnCellSpawn(CellSpawnEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _cellSpawnEffect?.PlaySpawnAnimation(eventData.Position, eventData.Coordinates, eventData.Delay);
            TrackEffectPerformance("CellSpawn");
        }

        private void OnBlockMovement(BlockMovementEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _movementEffect?.PlayMovementEffect(eventData.BlockTransform, eventData.StartPosition, 
                eventData.EndPosition, eventData.Duration);
            TrackEffectPerformance("BlockMovement");
        }

        private void OnBlockArrival(BlockArrivalEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _arrivalEffect?.PlayArrivalEffect(eventData.Position, eventData.BlockTransform, eventData.IsSpecialBlock);
            TrackEffectPerformance("BlockArrival");
        }

        private void OnMergeStart(BlockMergeStartEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _mergeEffect?.PlayMergeStartEffect(eventData.BlockPositions.ToArray(), eventData.MergeCenter);
            TrackEffectPerformance("MergeStart");
        }

        private void OnMergeComplete(BlockMergeCompleteEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _mergeEffect?.PlayMergeCompleteEffect(eventData.Position, eventData.NewBlock, eventData.MergedCount);
            TrackEffectPerformance("MergeComplete");
        }

        private void OnScoreUpdate(ScoreUpdateEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _scoreEffect?.PlayScoreUpdateEffect(eventData.PreviousScore, eventData.NewScore, eventData.SourcePosition);
            TrackEffectPerformance("ScoreUpdate");
        }

        private void OnFloatingScore(FloatingScoreEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _scoreEffect?.PlayFloatingScoreEffect(eventData.StartPosition, eventData.ScoreValue, eventData.TextColor);
            TrackEffectPerformance("FloatingScore");
        }

        private void OnScoreMilestone(ScoreMilestoneEvent eventData)
        {
            if (!ShouldPlayEffect()) return;
            
            _scoreEffect?.PlayMilestoneEffect(eventData.Score, eventData.MilestoneThreshold, eventData.MilestoneType);
            TrackEffectPerformance("ScoreMilestone");
        }

        private void OnVariantRequest(EffectVariantRequestEvent eventData)
        {
            if (_effectsSettings.EnableABTesting)
            {
                SwitchToVariant(eventData.RequestedVariant);
            }
        }

        #endregion

        #region Performance Management

        private bool ShouldPlayEffect()
        {
            if (_activeEffectCount >= _effectsSettings.MaxConcurrentEffects)
            {
                return false;
            }

            _activeEffectCount++;
            return true;
        }

        private void TrackEffectPerformance(string effectType)
        {
            if (!_effectsSettings.EnablePerformanceMonitoring) return;

            var startTime = Time.realtimeSinceStartup;
            // Performance tracking would be implemented here
            var executionTime = Time.realtimeSinceStartup - startTime;
            
            _effectPerformance[effectType] = executionTime;
        }

        public void OnEffectComplete()
        {
            _activeEffectCount = Mathf.Max(0, _activeEffectCount - 1);
        }

        public float GetCullingDistance()
        {
            return _effectsSettings?.EffectCullingDistance ?? 20f;
        }

        public Dictionary<string, float> GetPerformanceMetrics()
        {
            return new Dictionary<string, float>(_effectPerformance);
        }

        #endregion

        #region A/B Testing

        public void SwitchToVariant(string variant)
        {
            _effectsSettings.SetVariant(variant);

            // Reinitialize effects with new variant
            InitializeEffects();

            Debug.Log($"[GameEffectsManager] Switched to variant: {variant}");
        }

        public string GetCurrentVariant()
        {
            return _effectsSettings.CurrentVariant;
        }

        #endregion

        #region Public Utility Methods

        /// <summary>
        /// Check if effects are enabled and manager is ready
        /// </summary>
        public bool IsReady()
        {
            return _effectsSettings != null && enabled;
        }

        /// <summary>
        /// Get effect configuration for external use
        /// </summary>
        public GameEffectsSettings GetEffectsSettings()
        {
            return _effectsSettings;
        }

        /// <summary>
        /// Manually trigger a cell spawn effect (for testing)
        /// </summary>
        public void TestCellSpawnEffect(Vector3 position)
        {
            var testEvent = new CellSpawnEvent
            {
                Position = position,
                Coordinates = Vector2Int.zero,
                Delay = 0f,
                EffectVariant = GetCurrentVariant()
            };
            OnCellSpawn(testEvent);
        }

        /// <summary>
        /// Manually trigger a score effect (for testing)
        /// </summary>
        public void TestScoreEffect(Vector3 position, int scoreValue)
        {
            var testEvent = new FloatingScoreEvent
            {
                StartPosition = position,
                ScoreValue = scoreValue,
                TextColor = Color.green,
                AnimateToScoreBox = true,
                EffectVariant = GetCurrentVariant()
            };
            OnFloatingScore(testEvent);
        }

        #endregion
    }
}
