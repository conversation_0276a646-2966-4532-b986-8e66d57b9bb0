using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using _Tasks.Events;
using _Tasks.NewFeature.VFX;

namespace Whatwapp.MergeSolitaire.Game.GameStates
{
    /// <summary>
    /// State responsible for handling VFX synchronization with block destruction
    /// Ensures visual effects are properly timed with actual game mechanics
    /// </summary>
    public class VFXState : BaseState
    {
        private bool _vfxCompleted;
        private bool _isProcessingVFX;
        private VFXManager _vfxManager;
        private Coroutine _vfxCoroutine;

        public bool VFXCompleted => _vfxCompleted;

        public VFXState(GameController gameController) : base(gameController)
        {
        }

        public override void OnEnter()
        {
            base.OnEnter();
            _vfxCompleted = false;
            _isProcessingVFX = false;
            _enterTime = Time.time;

            // Find VFXManager if not already cached
            if (_vfxManager == null)
            {
                _vfxManager = VFXManager.Instance;
                if (_vfxManager == null)
                {
                    Debug.LogWarning("[VFXState] VFXManager not found! VFX effects will be skipped.");
                    _vfxCompleted = true;
                    return;
                }
            }

            // Subscribe to VFX sync events
            EventManager.Subscribe<VFXSyncEvent>(OnVFXSyncRequested);
            EventManager.Subscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);

            Debug.Log("[VFXState] Entered VFX state, waiting for bomb destruction events...");

            // Start a short timeout to complete immediately if no bomb events occur
            _gameController.StartCoroutine(VFXTimeoutCoroutine());
        }

        public override void OnExit()
        {
            base.OnExit();

            // Unsubscribe from events
            EventManager.Unsubscribe<VFXSyncEvent>(OnVFXSyncRequested);
            EventManager.Unsubscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);

            // Stop any active VFX coroutines
            if (_vfxCoroutine != null)
            {
                _gameController.StopCoroutine(_vfxCoroutine);
                _vfxCoroutine = null;
            }

            _isProcessingVFX = false;

            // Reset the VFX processing flag in GameController
            _gameController.ResetVFXProcessing();
        }

        public override void Update()
        {
            // This state waits for VFX events and processes them
            // The state completes when all VFX processing is done
        }

        private float _enterTime;

        /// <summary>
        /// Handle bomb destruction marked events - prepare for synchronized VFX
        /// </summary>
        private void OnBombDestructionMarked(BombDestructionMarkedEvent eventData)
        {
            if (_isProcessingVFX) return;

            Debug.Log($"[VFXState] Bomb destruction marked for {eventData.BlockPositions.Count} blocks");

            // Start VFX processing coroutine using GameController
            _vfxCoroutine = _gameController.StartCoroutine(ProcessBombVFXCoroutine(eventData));
        }

        /// <summary>
        /// Handle VFX sync events for immediate effect triggering
        /// </summary>
        private void OnVFXSyncRequested(VFXSyncEvent eventData)
        {
            if (_vfxManager == null) return;

            Debug.Log($"[VFXState] VFX sync requested for {eventData.EffectPositions.Count} positions");

            // Trigger immediate effects
            if (eventData.TriggerSubExplosions)
            {
                _vfxCoroutine = _gameController.StartCoroutine(TriggerSubExplosionsCoroutine(eventData.EffectPositions));
            }

            if (eventData.TriggerScreenShake)
            {
                _vfxManager.TriggerScreenShake();
            }
        }

        /// <summary>
        /// Process bomb VFX with proper timing synchronization
        /// </summary>
        private IEnumerator ProcessBombVFXCoroutine(BombDestructionMarkedEvent eventData)
        {
            _isProcessingVFX = true;

            // Trigger main explosion effect immediately
            if (_vfxManager != null)
            {
                _vfxManager.TriggerScreenShake();
                Debug.Log($"[VFXState] Main explosion triggered at {eventData.ExplosionCenter}");
            }

            // Wait for the expected destruction delay to synchronize with actual block destruction
            yield return new WaitForSeconds(eventData.ExpectedDestructionDelay);

            // Trigger sub-explosions at the exact moment blocks are destroyed
            if (eventData.BlockPositions != null && eventData.BlockPositions.Count > 0)
            {
                yield return _gameController.StartCoroutine(TriggerSubExplosionsCoroutine(eventData.BlockPositions));
            }

            // Wait additional time proportional to the amount of sub-explosions so all particles finish
            float extraBuffer = Mathf.Clamp(eventData.BlockPositions?.Count ?? 0, 1, 20) * 0.05f; // 50 ms per particle capped
            yield return new WaitForSeconds(extraBuffer);

            // VFX processing complete
            _isProcessingVFX = false;
            _vfxCompleted = true;

            Debug.Log("[VFXState] VFX processing completed - all effects finished");
        }

        /// <summary>
        /// Trigger sub-explosion effects with staggered timing
        /// </summary>
        private IEnumerator TriggerSubExplosionsCoroutine(List<Vector3> positions)
        {
            if (_vfxManager == null) yield break;

            const float subExplosionDelay = 0.1f;
            const float subExplosionSpread = 0.05f;

            for (int i = 0; i < positions.Count; i++)
            {
                // Add slight random delay for more natural effect
                float delay = i * subExplosionDelay + Random.Range(0f, subExplosionSpread);
                yield return new WaitForSeconds(delay);

                // Trigger sub-explosion effect at position
                _vfxManager.TriggerSubExplosionAt(positions[i]);
                Debug.Log($"[VFXState] Sub-explosion triggered at {positions[i]}");
            }
        }

        /// <summary>
        /// Force complete VFX processing (for emergency state transitions)
        /// </summary>
        public void ForceComplete()
        {
            if (_vfxCoroutine != null)
            {
                _gameController.StopCoroutine(_vfxCoroutine);
                _vfxCoroutine = null;
            }

            _isProcessingVFX = false;
            _vfxCompleted = true;

            Debug.Log("[VFXState] VFX processing force completed");
        }



        /// <summary>
        /// Timeout coroutine to complete VFX state quickly if no bomb events occur
        /// </summary>
        private IEnumerator VFXTimeoutCoroutine()
        {
            // Wait a very short time for bomb events to be processed
            // Wait a bit longer (half a second) to give bomb explosions time to raise events
            yield return new WaitForSeconds(0.5f);

            // If no VFX processing has started, complete immediately
            if (!_isProcessingVFX && !_vfxCompleted)
            {
                _vfxCompleted = true;
                Debug.Log("[VFXState] No VFX events received, completing immediately");
            }
        }

        /// <summary>
        /// Check if VFX state can transition to next state
        /// </summary>
        public bool CanTransition()
        {
            return _vfxCompleted && !_isProcessingVFX;
        }
    }
}
