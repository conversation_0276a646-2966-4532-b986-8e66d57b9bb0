using UnityEngine;
using _Tasks.Events;
using _Tasks.NewFeature.Managers;

namespace _Tasks.NewFeature.Integration
{
    /// <summary>
    /// Integration component that bridges the existing game systems with the new effects system
    /// This component should be added to the GameController to enable automatic effect triggering
    /// </summary>
    public class GameEffectsIntegration : MonoBehaviour
    {
        [Header("Integration Settings")]
        [SerializeField] private bool _enableCellSpawnEffects = true;
        [SerializeField] private bool _enableMovementEffects = true;
        [SerializeField] private bool _enableArrivalEffects = true;
        [SerializeField] private bool _enableMergeEffects = true;
        [SerializeField] private bool _enableScoreEffects = true;
        
        [Header("Effect Timing")]
        [SerializeField] private float _cellSpawnDelay = 0.05f;
        [SerializeField] private float _arrivalEffectDelay = 0.1f;
        
        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        private GameEffectsManager _effectsManager;

        private void Start()
        {
            _effectsManager = GameEffectsManager.Instance;
            if (_effectsManager == null)
            {
                Debug.LogWarning("[GameEffectsIntegration] GameEffectsManager not found! Effects will not work.");
            }
        }

        #region Public Methods for Game Integration

        /// <summary>
        /// Trigger cell spawn effect - call this when cells are created
        /// </summary>
        /// <param name="position">World position of the cell</param>
        /// <param name="coordinates">Grid coordinates</param>
        /// <param name="delay">Optional delay before playing effect</param>
        public void TriggerCellSpawnEffect(Vector3 position, Vector2Int coordinates, float delay = 0f)
        {
            if (!_enableCellSpawnEffects || _effectsManager == null) return;

            var effectEvent = new CellSpawnEvent
            {
                Position = position,
                Coordinates = coordinates,
                Delay = delay + _cellSpawnDelay,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered cell spawn effect at {position}");
        }

        /// <summary>
        /// Trigger block movement effect - call this when blocks start moving
        /// </summary>
        /// <param name="blockTransform">Transform of the moving block</param>
        /// <param name="startPosition">Starting position</param>
        /// <param name="endPosition">Destination position</param>
        /// <param name="duration">Movement duration</param>
        /// <param name="isSpecialBlock">Whether this is a special block</param>
        public void TriggerBlockMovementEffect(Transform blockTransform, Vector3 startPosition, 
            Vector3 endPosition, float duration, bool isSpecialBlock = false)
        {
            if (!_enableMovementEffects || _effectsManager == null) return;

            var effectEvent = new BlockMovementEvent
            {
                BlockTransform = blockTransform,
                StartPosition = startPosition,
                EndPosition = endPosition,
                Duration = duration,
                IsSpecialBlock = isSpecialBlock,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered movement effect for block from {startPosition} to {endPosition}");
        }

        /// <summary>
        /// Trigger block arrival effect - call this when blocks reach their destination
        /// </summary>
        /// <param name="position">Arrival position</param>
        /// <param name="coordinates">Grid coordinates</param>
        /// <param name="blockTransform">Block transform</param>
        /// <param name="isSpecialBlock">Whether this is a special block</param>
        public void TriggerBlockArrivalEffect(Vector3 position, Vector2Int coordinates, 
            Transform blockTransform, bool isSpecialBlock = false)
        {
            if (!_enableArrivalEffects || _effectsManager == null) return;

            // Add slight delay to ensure block has settled
            StartCoroutine(DelayedArrivalEffect(position, coordinates, blockTransform, isSpecialBlock));
        }

        /// <summary>
        /// Trigger merge start effect - call this when blocks begin merging
        /// </summary>
        /// <param name="blockPositions">Positions of blocks being merged</param>
        /// <param name="mergeCenter">Center point of the merge</param>
        /// <param name="blockCount">Number of blocks being merged</param>
        public void TriggerMergeStartEffect(Vector3[] blockPositions, Vector3 mergeCenter, int blockCount)
        {
            if (!_enableMergeEffects || _effectsManager == null) return;

            var effectEvent = new BlockMergeStartEvent
            {
                BlockPositions = new System.Collections.Generic.List<Vector3>(blockPositions),
                MergeCenter = mergeCenter,
                BlockCount = blockCount,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered merge start effect for {blockCount} blocks");
        }

        /// <summary>
        /// Trigger merge complete effect - call this when merge animation finishes
        /// </summary>
        /// <param name="position">Position of the new merged block</param>
        /// <param name="newBlock">Transform of the newly created block</param>
        /// <param name="mergedCount">Number of blocks that were merged</param>
        public void TriggerMergeCompleteEffect(Vector3 position, Transform newBlock, int mergedCount)
        {
            if (!_enableMergeEffects || _effectsManager == null) return;

            var effectEvent = new BlockMergeCompleteEvent
            {
                Position = position,
                NewBlock = newBlock,
                MergedCount = mergedCount,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered merge complete effect at {position}");
        }

        /// <summary>
        /// Trigger score update effect - call this when score changes
        /// </summary>
        /// <param name="previousScore">Previous score value</param>
        /// <param name="newScore">New score value</param>
        /// <param name="sourcePosition">Position where score was earned</param>
        /// <param name="isFromMerge">Whether score came from merging</param>
        /// <param name="isFromFoundation">Whether score came from foundation</param>
        public void TriggerScoreUpdateEffect(int previousScore, int newScore, Vector3 sourcePosition, 
            bool isFromMerge = false, bool isFromFoundation = false)
        {
            if (!_enableScoreEffects || _effectsManager == null) return;

            var scoreDelta = newScore - previousScore;
            if (scoreDelta <= 0) return; // Only trigger for positive score changes

            var effectEvent = new ScoreUpdateEvent
            {
                PreviousScore = previousScore,
                NewScore = newScore,
                ScoreDelta = scoreDelta,
                SourcePosition = sourcePosition,
                IsFromMerge = isFromMerge,
                IsFromFoundation = isFromFoundation,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            // Also trigger floating score text
            TriggerFloatingScoreEffect(sourcePosition, scoreDelta, isFromFoundation);

            // Check for milestones
            CheckScoreMilestone(newScore);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered score update effect: +{scoreDelta} at {sourcePosition}");
        }

        /// <summary>
        /// Trigger floating score text effect
        /// </summary>
        /// <param name="startPosition">Starting position for the text</param>
        /// <param name="scoreValue">Score value to display</param>
        /// <param name="isFromFoundation">Whether this is from foundation completion</param>
        public void TriggerFloatingScoreEffect(Vector3 startPosition, int scoreValue, bool isFromFoundation = false)
        {
            if (!_enableScoreEffects || _effectsManager == null) return;

            var textColor = isFromFoundation ? Color.yellow : Color.green;

            var effectEvent = new FloatingScoreEvent
            {
                StartPosition = startPosition,
                ScoreValue = scoreValue,
                TextColor = textColor,
                AnimateToScoreBox = true,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);
        }

        #endregion

        #region Private Methods

        private System.Collections.IEnumerator DelayedArrivalEffect(Vector3 position, Vector2Int coordinates, 
            Transform blockTransform, bool isSpecialBlock)
        {
            yield return new WaitForSeconds(_arrivalEffectDelay);

            var effectEvent = new BlockArrivalEvent
            {
                Position = position,
                Coordinates = coordinates,
                BlockTransform = blockTransform,
                IsSpecialBlock = isSpecialBlock,
                EffectVariant = _effectsManager.GetCurrentVariant()
            };

            EventManager.Broadcast(effectEvent);

            if (_debugMode)
                Debug.Log($"[GameEffectsIntegration] Triggered arrival effect at {position}");
        }

        private void CheckScoreMilestone(int newScore)
        {
            // Simple milestone detection - can be enhanced with more sophisticated logic
            var milestones = new int[] { 100, 500, 1000, 2500, 5000, 10000, 25000, 50000, 100000 };
            
            foreach (var milestone in milestones)
            {
                if (newScore >= milestone && (newScore - milestone) < 100) // Recently crossed this milestone
                {
                    var effectEvent = new ScoreMilestoneEvent
                    {
                        Score = newScore,
                        MilestoneThreshold = milestone,
                        MilestoneType = GetMilestoneType(milestone),
                        IsNewHighScore = false, // This would need to be determined by the caller
                        EffectVariant = _effectsManager.GetCurrentVariant()
                    };

                    EventManager.Broadcast(effectEvent);

                    if (_debugMode)
                        Debug.Log($"[GameEffectsIntegration] Milestone reached: {milestone}");
                    
                    break; // Only trigger one milestone at a time
                }
            }
        }

        private string GetMilestoneType(int milestone)
        {
            if (milestone < 1000) return "hundred";
            if (milestone < 10000) return "thousand";
            return "major";
        }

        #endregion

        #region A/B Testing Support

        /// <summary>
        /// Switch to a specific effect variant for A/B testing
        /// </summary>
        /// <param name="variant">Variant name to switch to</param>
        public void SwitchEffectVariant(string variant)
        {
            if (_effectsManager != null)
            {
                _effectsManager.SwitchToVariant(variant);
                Debug.Log($"[GameEffectsIntegration] Switched to variant: {variant}");
            }
        }

        /// <summary>
        /// Get the current effect variant
        /// </summary>
        /// <returns>Current variant name</returns>
        public string GetCurrentVariant()
        {
            return _effectsManager?.GetCurrentVariant() ?? "default";
        }

        #endregion
    }
}
