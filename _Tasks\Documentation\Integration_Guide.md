# Game Effects Integration Guide

## Overview

This guide explains how to integrate the new Game Effects system with the existing MergeSolitaire codebase to enable enhanced visual feedback with A/B testing support.

## Step-by-Step Integration

### 1. Setup GameEffectsManager

Add the GameEffectsManager to your scene:

```csharp
// In GameController.cs, add reference
[Header("Effects")]
[SerializeField] private GameEffectsManager _gameEffectsManager;
[SerializeField] private GameEffectsIntegration _effectsIntegration;
```

### 2. Integrate with Existing States

#### MoveBlocksState Integration

<augment_code_snippet path="Scripts/Whatwapp/MergeSolitaire/Game/GameStates/MoveBlocksState.cs" mode="EXCERPT">
````csharp
private void MoveBlocks()
{
    var sequence = DOTween.Sequence();
    
    foreach (var cell in _movingCells)
    {
        var block = cell.Block;
        var targetCell = _board.GetCell(cell.Coordinates.x, cell.Coordinates.y + 1);
        
        // NEW: Trigger movement effect
        var effectsIntegration = FindObjectOfType<GameEffectsIntegration>();
        if (effectsIntegration != null)
        {
            effectsIntegration.TriggerBlockMovementEffect(
                block.transform,
                cell.Position,
                targetCell.Position,
                _animationSettings.BlockMoveDuration,
                block is ISpecialBlock
            );
        }
        
        targetCell.Block = block;
        cell.Block = null;
        sequence.Join(
            DOTween.Sequence()
                .AppendInterval(_animationSettings.BlockMoveDelay)
                .Append(block.transform.DOMove(targetCell.Position, _animationSettings.BlockMoveDuration))
                .OnComplete(() =>
                {
                    block.Visual.ShakeScale();

                    // NEW: Trigger arrival effect
                    if (effectsIntegration != null)
                    {
                        effectsIntegration.TriggerBlockArrivalEffect(
                            targetCell.Position,
                            targetCell.Coordinates,
                            block.transform,
                            block is ISpecialBlock
                        );
                    }

                    if (block is ISpecialBlock specialBlock)
                    {
                        specialBlock.OnMovementStopped();
                    }
                }));
    }
    sequence.OnComplete(() =>
    {
        _isMovingBlocks = false;
    });
    sequence.Play();
}
````
</augment_code_snippet>

#### MergeBlocksState Integration

<augment_code_snippet path="Scripts/Whatwapp/MergeSolitaire/Game/GameStates/MergeBlocksState.cs" mode="EXCERPT">
````csharp
// In the merge loop, add effect triggers
foreach (var group in mergeableGroups)
{
    // NEW: Trigger merge start effect
    var effectsIntegration = FindObjectOfType<GameEffectsIntegration>();
    if (effectsIntegration != null)
    {
        var blockPositions = group.Select(cell => cell.Position).ToArray();
        var mergeCenter = group[0].Position; // Use first cell as center
        effectsIntegration.TriggerMergeStartEffect(blockPositions, mergeCenter, group.Count);
    }
    
    // ... existing merge logic ...
    
    groupSequence.OnComplete(() =>
    {
        var nextValue = value.Next(true);
        var randomSeed = EnumUtils.GetRandom<BlockSeed>();
        var newBlock = _blockFactory.Create(nextValue, seed);
        firstCell.Block = newBlock;
        newBlock.transform.localScale = Vector3.zero;
        newBlock.transform.DOScale(Vector3.one, _animationSettings.MergeDuration).SetEase(Ease.OutBack);

        // NEW: Trigger merge complete effect
        if (effectsIntegration != null)
        {
            effectsIntegration.TriggerMergeCompleteEffect(
                firstCell.Position,
                newBlock.transform,
                group.Count
            );
        }
        
        // ... rest of existing logic ...
    });
}
````
</augment_code_snippet>

#### GameController Score Integration

<augment_code_snippet path="Scripts/Whatwapp/MergeSolitaire/Game/GameController.cs" mode="EXCERPT">
````csharp
public int Score
{
    get => _score;
    set
    {
        var previousScore = _score;
        _score = value;
        
        if (_score > _highScore)
        {
            _highScore = _score;
            PlayerPrefs.SetInt(Consts.PREFS_HIGHSCORE, _highScore);
        }
        
        _scoreBox.SetScore(_score);
        PlayerPrefs.SetInt(Consts.PREFS_LAST_SCORE, _score);
        
        // NEW: Trigger score effects
        var effectsIntegration = FindObjectOfType<GameEffectsIntegration>();
        if (effectsIntegration != null && value > previousScore)
        {
            // Determine source position (this would need to be passed from the calling context)
            Vector3 sourcePosition = Vector3.zero; // You'll need to modify callers to provide this
            
            effectsIntegration.TriggerScoreUpdateEffect(
                previousScore,
                value,
                sourcePosition,
                isFromMerge: true // This would also need to be determined from context
            );
        }
    }
}
````
</augment_code_snippet>

### 3. Grid Generation Integration

For cell spawn effects, integrate with the GridBuilder:

```csharp
// In GridBuilder.cs or wherever cells are created
public void CreateCell(Vector2Int coordinates, Vector3 position)
{
    // ... existing cell creation logic ...
    
    // NEW: Trigger cell spawn effect
    var effectsIntegration = FindObjectOfType<GameEffectsIntegration>();
    if (effectsIntegration != null)
    {
        float delay = (coordinates.x + coordinates.y) * 0.05f; // Staggered animation
        effectsIntegration.TriggerCellSpawnEffect(position, coordinates, delay);
    }
}
```

### 4. A/B Testing Setup

#### Initialize A/B Testing

```csharp
// In GameController.Start() or similar initialization
private void InitializeABTesting()
{
    var variantManager = EffectVariantManager.Instance;
    if (variantManager != null)
    {
        // Get player's assigned variant
        string playerId = SystemInfo.deviceUniqueIdentifier;
        string variant = variantManager.GetPlayerVariant(playerId);
        
        // Apply the variant
        variantManager.SetCurrentVariant(variant);
        
        // Track assignment for analytics
        variantManager.TrackVariantAssignment(playerId, variant, "game_effects_v1");
    }
}
```

#### Remote Config Integration (Optional)

```csharp
// Example Firebase Remote Config integration
private void ApplyRemoteConfigEffects()
{
    var variantManager = EffectVariantManager.Instance;
    if (variantManager != null)
    {
        variantManager.ApplyRemoteConfigVariant("game_effects_variant", "default");
    }
}
```

### 5. Performance Optimization

#### Effect Culling

The system automatically handles performance optimization, but you can configure it:

```csharp
// In GameEffectsSettings
_maxConcurrentEffects = 30; // Reduce for lower-end devices
_effectCullingDistance = 10f; // Cull effects far from camera
_enablePerformanceMonitoring = true; // Track performance metrics
```

#### Device-Specific Variants

```csharp
// Example device-specific variant assignment
private string GetDeviceAppropriateVariant()
{
    if (SystemInfo.systemMemorySize < 2048) // Less than 2GB RAM
    {
        return "minimal";
    }
    else if (SystemInfo.graphicsMemorySize > 1024) // Good GPU
    {
        return "enhanced";
    }
    else
    {
        return "default";
    }
}
```

## Configuration Examples

### Creating Effect Variants

1. **Default Variant**: Balanced effects for most devices
2. **Enhanced Variant**: More particles, longer animations, additional visual elements
3. **Minimal Variant**: Reduced effects for performance-constrained devices

### ScriptableObject Setup

Create multiple GameEffectsSettings assets:
- `GameEffectsSettings_Default.asset`
- `GameEffectsSettings_Enhanced.asset`
- `GameEffectsSettings_Minimal.asset`

## Testing and Debugging

### Manual Testing

```csharp
// Test effects manually in editor
[ContextMenu("Test Cell Spawn")]
private void TestCellSpawn()
{
    var manager = GameEffectsManager.Instance;
    if (manager != null)
    {
        manager.TestCellSpawnEffect(transform.position);
    }
}

[ContextMenu("Test Score Effect")]
private void TestScoreEffect()
{
    var manager = GameEffectsManager.Instance;
    if (manager != null)
    {
        manager.TestScoreEffect(transform.position, 100);
    }
}
```

### A/B Testing Debug

```csharp
// Force specific variant for testing
var variantManager = EffectVariantManager.Instance;
variantManager.ForceVariantForTesting("enhanced");

// Get variant statistics
var stats = variantManager.GetVariantStatistics();
foreach (var kvp in stats)
{
    Debug.Log($"Variant {kvp.Key}: {kvp.Value} assignments");
}
```

## Analytics Integration

Track effect performance and user engagement:

```csharp
// Example analytics events
Analytics.CustomEvent("effect_variant_performance", new Dictionary<string, object>
{
    {"variant", currentVariant},
    {"avg_fps", averageFPS},
    {"effect_count", totalEffectsPlayed},
    {"session_duration", sessionTime}
});
```

## Troubleshooting

### Common Issues

1. **Effects not playing**: Check GameEffectsManager is in scene and configured
2. **Performance issues**: Reduce maxConcurrentEffects or switch to minimal variant
3. **Variant not switching**: Ensure EffectVariantManager is properly initialized
4. **Missing references**: Check effect prefabs are assigned in GameEffectsManager

### Debug Logging

Enable debug mode in components for detailed logging:
- GameEffectsManager: `_debugMode = true`
- EffectVariantManager: `_debugMode = true`
- Individual effects: `_debugMode = true`

## Next Steps

1. Implement remaining effect types (movement, arrival, merge, score)
2. Create effect prefabs with particle systems and visual components
3. Set up A/B testing experiments
4. Monitor performance metrics and user engagement
5. Iterate on effect designs based on data
