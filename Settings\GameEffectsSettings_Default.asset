%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0, type: 3}
  m_Name: GameEffectsSettings_Default
  m_EditorClassIdentifier: 
  _currentVariant: default
  _enableABTesting: 1
  _cellSpawnVariants:
  - _variantName: default
    _isEnabled: 1
    _intensityMultiplier: 1
    scaleCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    alphaCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 2
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    duration: 0.3
    maxDelay: 0.1
    enableParticles: 1
    enableGlow: 0
    spawnColor: {r: 1, g: 1, b: 1, a: 1}
    glowIntensity: 1
  - _variantName: enhanced
    _isEnabled: 1
    _intensityMultiplier: 1.5
    scaleCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 3
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.7
        value: 1.2
        inSlope: 0
        outSlope: -1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    alphaCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 3
        outSlope: 3
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    duration: 0.5
    maxDelay: 0.15
    enableParticles: 1
    enableGlow: 1
    spawnColor: {r: 0.8, g: 1, b: 0.9, a: 1}
    glowIntensity: 1.5
  _movementVariants:
  - _variantName: default
    _isEnabled: 1
    _intensityMultiplier: 1
    movementCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    enableTrail: 0
    enableAnticipation: 0
    trailColor: {r: 1, g: 1, b: 1, a: 0.5}
    trailDuration: 0.2
    trailSegments: 10
    anticipationScale: 1.1
    anticipationDuration: 0.1
  - _variantName: enhanced
    _isEnabled: 1
    _intensityMultiplier: 1.2
    movementCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.8
        value: 0.9
        inSlope: 0.5
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    enableTrail: 1
    enableAnticipation: 1
    trailColor: {r: 0.5, g: 0.8, b: 1, a: 0.7}
    trailDuration: 0.3
    trailSegments: 15
    anticipationScale: 1.15
    anticipationDuration: 0.15
  _arrivalVariants:
  - _variantName: default
    _isEnabled: 1
    _intensityMultiplier: 1
    enableScreenShake: 1
    enableParticles: 1
    enableRipple: 0
    shakeIntensity: 0.05
    shakeDuration: 0.1
    particleCount: 15
    particleSpeed: 3
    particleColor: {r: 1, g: 1, b: 1, a: 1}
    rippleRadius: 1.5
    rippleDuration: 0.4
  - _variantName: enhanced
    _isEnabled: 1
    _intensityMultiplier: 1.3
    enableScreenShake: 1
    enableParticles: 1
    enableRipple: 1
    shakeIntensity: 0.08
    shakeDuration: 0.15
    particleCount: 25
    particleSpeed: 5
    particleColor: {r: 1, g: 0.8, b: 0.6, a: 1}
    rippleRadius: 2
    rippleDuration: 0.6
  _mergeVariants:
  - _variantName: default
    _isEnabled: 1
    _intensityMultiplier: 1
    convergeDuration: 0.25
    explosionDuration: 0.15
    convergeEase:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    enableEnergyBeams: 1
    enableExplosion: 1
    enableScreenFlash: 0
    beamColor: {r: 0.5, g: 0.8, b: 1, a: 1}
    beamWidth: 0.08
    beamDuration: 0.2
    explosionParticles: 30
    explosionRadius: 1.2
    explosionColor: {r: 1, g: 0.8, b: 0.4, a: 1}
  _scoreVariants:
  - _variantName: default
    _isEnabled: 1
    _intensityMultiplier: 1
    counterAnimationSpeed: 8
    enableColorChange: 1
    enableScaleEffect: 1
    floatingDuration: 1.2
    floatingOffset: {x: 0, y: 1.5, z: 0}
    floatingCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 1
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    milestoneThresholds: [100, 500, 1000, 5000, 10000]
    enableMilestoneParticles: 1
    enableMilestoneSound: 1
    milestoneColor: {r: 1, g: 0.8, b: 0, a: 1}
    positiveScoreColor: {r: 0.2, g: 1, b: 0.3, a: 1}
    milestoneTextColor: {r: 1, g: 0.8, b: 0, a: 1}
    normalTextColor: {r: 1, g: 1, b: 1, a: 1}
  _enablePerformanceMonitoring: 1
  _maxConcurrentEffects: 50
  _effectCullingDistance: 15
